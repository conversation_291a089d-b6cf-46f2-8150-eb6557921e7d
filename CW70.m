%%%%清理工作区,图片对话框,命令行...%20231004edit by CK， 6(12行) 处差异。
% 修改软件人机界面，加入文件路径输入对话框，20240318
clear all
clc
close all

%%%设置待处理数据路径
project_name = fullfile('测试数据/600M202505150002');

%%%常用数据设置
R2D = 57.295779513082320876798154814105;%弧度转度
D2R = 0.017453292519943295769236907684886;%度转弧度
Re = 6378137;%地球半长轴半径：m
g0 = 9.78049;%标准重力加速度：m/s2
MIN_DATA = 1e-5;%最小数据，用于除零保护
e = 0.0033528131778969144060323814696721;%%地球扁率
ee = 0.006705626355793828812064762938;%% 表示2 * e
eee = 0.010058439533690743218097144407;%%表示3 * e
pipi = 6.283185307179586476925286766559;%%表示2 * pi
Ree = 21384.701784031891958948155445543;%%表示Re * e
Reee = 42769.403568063783917896310891087;%%表示Re * 2e
Reeee = 64154.10535209567587684446633663;%%表示Re * 3e
invRe2 = 3.13571188577479599450435134899e-7;%%%表示 2/Re
pidiv2 = 1.5707963267948966192313216916398; %%%表示pi/2
wie =  7.292115e-5;%地球自转角速率：rad/s

%%%根据标定地区的地理位置修改
lati = 22.75129360 * D2R;
h = 12.47;
gn = (g0 + 0.051723143316 * sin(lati)^2 - 0.000057704891 * sin(2 * lati)^2) * (1 - h * invRe2);
% gn = 9.801433450668984;

%%%%设置数据精度
format long g

% ↓↓↓存放全温转台标定数据的路径↓↓↓
Base_Path_Str_16   = fullfile(project_name,'turnable_data');
Base_Path_Str_24CW = fullfile(project_name,'turnable_data');

%%数据起始列
start_col = 1;
%%%数据范围
data_row_sta = 5;    %%结束行数
data_row_vend = data_row_sta+200*36;

%%%温度范围字符串
Temperature_Str_List = ["F40","F30","F20","F10","Z0","Z10","Z20","Z30","Z40","Z50","Z60","Z70"];
%%Temperature_Str_List = ["Z10"];
%%%保存各个温度点的刻度因子、安装误差角、零偏以及温度点温度数据
Gyro_Temperature_All = zeros(length(Temperature_Str_List),4);
Acc_Temperature_All = zeros(length(Temperature_Str_List),4);
Gyro_Bias_All = zeros(length(Temperature_Str_List),3);
Acc_Bias_All = zeros(length(Temperature_Str_List),3);
K_g_all = zeros(3,3,length(Temperature_Str_List));
K_a_all = zeros(3,3,length(Temperature_Str_List));
Res_g_all = zeros(length(Temperature_Str_List),18);
Res_a_all = zeros(length(Temperature_Str_List),48);
Gyro_Scale_Factor_all = zeros(length(Temperature_Str_List),3);
Acc_Scale_Factor_all = zeros(length(Temperature_Str_List),3);

for i = 1 :length(Temperature_Str_List)%%%循环计算每个温度点的对应标定补偿参数

    fileFolderPath = fullfile(project_name,'turnable_data');
    files = dir(fullfile(fileFolderPath, '*.csv'));
    for ii = 1:length(files)
        filename = files(ii).name;
        
        % 使用正则表达式提取i角度和o角度
        anglePattern = 'wx_i(\d+)o(\d+)_';
        angleTokens = regexp(filename, anglePattern, 'tokens');
        ratePattern  = 'w([a-zA-Z]+)_(-?\d+)_';
        rateTokens = regexp(filename, ratePattern, 'tokens');
        
        if ~isempty(angleTokens)
            % 提取角度信息
            angles = angleTokens{1};
            iAngle = angles{1};
            oAngle = angles{2};
    
            % 构造变量名
            varName = ['Data' iAngle oAngle];
    
            % 读取csv文件中的数据
            data = readmatrix(fullfile(fileFolderPath, filename));
    
            % 动态地将数据分配给变量
            assignin('base', varName, data);
    
            % 打印读取信息
            fprintf('读取文件 %s 到变量 %s 中\n', filename, varName);
        end
    
        if ~isempty(rateTokens)
            % 提取角速率信息
            rotate_axis = rateTokens{1}{1};
            rotate_rate = rateTokens{1}{2};    
            if str2double(rotate_rate) > 0
                rate_symbol = 'p';
            else
                rate_symbol = 'n';
                rotate_rate = rotate_rate(2:end);
            end
    
            % 构造变量名
            varName = strcat('Data', upper(rotate_axis), rate_symbol, rotate_rate);
    
            % 读取csv文件中的数据
            data = readmatrix(fullfile(fileFolderPath, filename));
    
            % 动态地将数据分配给变量
            assignin('base', varName, data);
    
            % 打印读取信息
            fprintf('读取文件 %s 到变量 %s 中\n', filename, varName);
        end
    end

    %%%求取每个位置速率平均输出
    MeanData00   = mean(Data00(data_row_sta:end,start_col:end));
    MeanData090  = mean(Data090(data_row_sta:end,start_col:end));
    MeanData0180 = mean(Data0180(data_row_sta:end,start_col:end));
    MeanData0270 = mean(Data0270(data_row_sta:end,start_col:end));

    MeanData900   = mean(Data900(data_row_sta:end,start_col:end));
    MeanData9090  = mean(Data9090(data_row_sta:end,start_col:end));
    MeanData90180 = mean(Data90180(data_row_sta:end,start_col:end));
    MeanData90270 = mean(Data90270(data_row_sta:end,start_col:end));

    MeanData1800   = mean(Data1800(data_row_sta:end,start_col:end));
    MeanData18090  = mean(Data18090(data_row_sta:end,start_col:end));
    MeanData180180 = mean(Data180180(data_row_sta:end,start_col:end));
    MeanData180270 = mean(Data180270(data_row_sta:end,start_col:end));

    MeanData2700   = mean(Data2700(data_row_sta:end,start_col:end));
    MeanData27090  = mean(Data27090(data_row_sta:end,start_col:end));
    MeanData270180 = mean(Data270180(data_row_sta:end,start_col:end));
    MeanData270270 = mean(Data270270(data_row_sta:end,start_col:end));

    MeanDataXp10 = mean(DataXp10(data_row_sta:data_row_vend,start_col:end));%%取整数圈
    MeanDataXn10 = mean(DataXn10(data_row_sta:data_row_vend,start_col:end));%%取整数圈

    MeanDataYp10 = mean(DataYp10(data_row_sta:data_row_vend,start_col:end));%%取整数圈
    MeanDataYn10 = mean(DataYn10(data_row_sta:data_row_vend,start_col:end));%%取整数圈

    MeanDataZp10 = mean(DataZp10(data_row_sta:data_row_vend,start_col:end));%%取整数圈
    MeanDataZn10 = mean(DataZn10(data_row_sta:data_row_vend,start_col:end));%%取整数圈

    Mean0   = (MeanData00 + MeanData090 + MeanData0180 + MeanData0270) / 4.0;
    Mean90  = (MeanData900 + MeanData9090 + MeanData90180 + MeanData90270) / 4.0;
    Mean180 = (MeanData1800 + MeanData18090 + MeanData180180 + MeanData180270) / 4.0;
    Mean270 = (MeanData2700 + MeanData27090 + MeanData270180 + MeanData270270) / 4.0;

    XDown = (MeanData090 + MeanData180270) / 2.0;
    XUp = (MeanData0270 + MeanData18090) / 2.0;

    YDown = (MeanData90270 + MeanData27090) / 2.0;   %20230921调换XUP/XDOWNZDown
    YUp  = (MeanData9090 + MeanData270270) / 2.0;%%ZUp
        
    ZUp = (MeanData00 + MeanData900 + MeanData1800 + MeanData2700) / 4.0;
    ZDown = (MeanData0180 + MeanData90180 + MeanData180180 + MeanData270180) / 4.0;
    
    Gyro_BiasXY = (ZUp(1,5:6) + ZDown(1,5:6)) / 2.0;
    GyroBiasZ = (YUp(1,7) + YDown(1,7)) / 2.0;
    
    Mean_16 = (Mean0 + Mean90 + Mean180 + Mean270) / 4.0;
    Gyro_Bias = [Gyro_BiasXY,GyroBiasZ];
    Acc_Bias = (XUp(2:4) + XDown(2:4) + YUp(2:4) + YDown(2:4) + ZUp(2:4) + ZDown(2:4)) / 6.0;
    %%此处修改了板卡温度取值，取加表Z，20230920
    Gyro_Board_Temperature = -50 + 10 * i;%%(MeanDataXp10(1,12) + MeanDataXn10(1,12) + MeanDataYp10(1,12) + MeanDataYn10(1,12) + MeanDataZp10(1,12) + MeanDataZn10(1,12)) / 6.0;
    %%%%Acc_Temperature = (Mean_16(1,10) + MeanDataXp10(1,10) + MeanDataXn10(1,10) + MeanDataYp10(1,10) + MeanDataYn10(1,10) + MeanDataZp10(1,10) + MeanDataZn10(1,10)) / 7.0;
    % TemperatureGyroX = (MeanDataXp10(1,7) + MeanDataXn10(1,7)) / 2.0;
    % TemperatureGyroY = (MeanDataYp10(1,8) + MeanDataYn10(1,8)) / 2.0;
    % TemperatureGyroZ = (MeanDataZp10(1,9) + MeanDataZn10(1,9)) / 2.0;
    
    Gyro_Temperature_All(i,1:4) = [Gyro_Board_Temperature,Gyro_Board_Temperature,Gyro_Board_Temperature,Gyro_Board_Temperature];
    
    
    Acc_Temperature_All(i,1:4) = [Gyro_Board_Temperature,Gyro_Board_Temperature,Gyro_Board_Temperature,Gyro_Board_Temperature];
    %%%此处修改为常用的3060S标定程序
    GyroXp10 = MeanDataXp10(1,5:7);
    GyroXn10 = MeanDataXn10(1,5:7);

    GyroYp10 = MeanDataYp10(1,5:7);  
    GyroYn10 = MeanDataYn10(1,5:7);

    GyroZp10 = MeanDataZp10(1,5:7);  
    GyroZn10 = MeanDataZn10(1,5:7);
    
    KGX = (GyroXp10 - GyroXn10) / 2.0 / 10.0;
    KGY = (GyroYp10 - GyroYn10) / 2.0 / 10.0;
    KGZ = (GyroZp10 - GyroZn10) / 2.0 / 10.0;
    
    K_g = [KGX',KGY',KGZ'];
  
    KAX = (XUp(1,2:4) - XDown(1,2:4)) / 2.0;
    KAY = (YUp(1,2:4) - YDown(1,2:4)) / 2.0;
    KAZ = (ZUp(1,2:4) - ZDown(1,2:4)) / 2.0;
    
    K_a = [KAX',KAY',KAZ'];

    Gyro_Bias_All(i,1:end) = Gyro_Bias;
    Acc_Bias_All(i,1:end) = Acc_Bias;

    K_g_all(1:end,1:end,i) = K_g;
    K_a_all(1:end,1:end,i) = K_a;
    
    Gyro_Scale_Factor_all(i,1) = K_g_all(1,1,i);
    Gyro_Scale_Factor_all(i,2) = K_g_all(2,2,i);
    Gyro_Scale_Factor_all(i,3) = K_g_all(3,3,i);
    
    Acc_Scale_Factor_all(i,1) = K_a_all(1,1,i);
    Acc_Scale_Factor_all(i,2) = K_a_all(2,2,i);
    Acc_Scale_Factor_all(i,3) = K_a_all(3,3,i);
end
% K_g = [KGX',KGY',KGZ'];
K_g_K = [K_g(1,1),0,0;0,K_g(2,2),0;0,0,K_g(3,3)];
K_g_2 = K_g_K^-1 * K_g;
K_g_std = K_g_2^-1;

K_a_K = [K_a(1,1),0,0;0,K_a(2,2),0;0,0,K_a(3,3)];
K_a_2 = K_a_K^-1 * K_a;
K_a_std = K_a_2^-1;

%%%%安装误差角以常温下的误差角为准，高低温16位置精度不高，不适用。
%%%%%K_g_std = [K_g(1:end,1) / K_g(1,1),K_g(1:end,2) / K_g(2,2),K_g(1:end,3) / K_g(3,3)];
%%%%%K_a_std = [K_a(1:end,1) / K_a(1,1),K_a(1:end,2) / K_a(2,2),K_a(1:end,3) / K_a(3,3)];
Bias_Correct_Val = [0,0,0];
Output_Excel_Path_Str = fullfile(project_name,''); %%%输出到默认工作目录
%% 将计算结果写入Excel表格保存，查看

Output_Excel_Filename = fullfile(Output_Excel_Path_Str, "标定参数.xlsx");

%%%%陀螺常温
title1 = {'Temperature/°C'};
column = {'X','Y','Z'};
writecell(title1,Output_Excel_Filename,'Sheet','陀螺常温','Range','A1');
writecell(column,Output_Excel_Filename,'Sheet','陀螺常温','Range','A2');
writematrix([25,25,25],Output_Excel_Filename,'Sheet','陀螺常温','Range','A3');

title2 = {'Board_Temperature/°C'};
column = {'Board'};
writecell(title2,Output_Excel_Filename,'Sheet','陀螺常温','Range','E1');
writecell(column,Output_Excel_Filename,'Sheet','陀螺常温','Range','E2');
writematrix(25.0,Output_Excel_Filename,'Sheet','陀螺常温','Range','E3');

title3 = {'Normal_Temperature_Bias/LSB'};
column = {'X','Y','Z'};
writecell(title3,Output_Excel_Filename,'Sheet','陀螺常温','Range','H1');
writecell(column,Output_Excel_Filename,'Sheet','陀螺常温','Range','H2');
writematrix(Gyro_Bias,Output_Excel_Filename,'Sheet','陀螺常温','Range','H3');

title4 = {'Normal_Temperature_Scale_Factor/LSB'};
column = {'X','Y','Z'};
writecell(title4,Output_Excel_Filename,'Sheet','陀螺常温','Range','M1');
writecell(column,Output_Excel_Filename,'Sheet','陀螺常温','Range','M2');
writematrix([K_g(1,1),K_g(2,2),K_g(3,3)],Output_Excel_Filename,'Sheet','陀螺常温','Range','M3');

title5 = {'Misalignment_Compensation_Matrix'};
writecell(title5,Output_Excel_Filename,'Sheet','陀螺常温','Range','A5');
writematrix(K_g_std,Output_Excel_Filename,'Sheet','陀螺常温','Range','A6');

title6 = {'Bias_Correct_Val'};
column = {'X','Y','Z'};
writecell(title6,Output_Excel_Filename,'Sheet','陀螺常温','Range','F5');
writecell(column,Output_Excel_Filename,'Sheet','陀螺常温','Range','F6');
writematrix(Bias_Correct_Val,Output_Excel_Filename,'Sheet','陀螺常温','Range','F7');

%%%%加速度计常温
title1 = {'Temperature/°C'};
column = {'X','Y','Z'};
writecell(title1,Output_Excel_Filename,'Sheet','加表常温','Range','A1');
writecell(column,Output_Excel_Filename,'Sheet','加表常温','Range','A2');
writematrix([25,25,25],Output_Excel_Filename,'Sheet','加表常温','Range','A3');

title2 = {'Board_Temperature/°C'};
column = {'Board'};
writecell(title2,Output_Excel_Filename,'Sheet','加表常温','Range','E1');
writecell(column,Output_Excel_Filename,'Sheet','加表常温','Range','E2');
writematrix(25,Output_Excel_Filename,'Sheet','加表常温','Range','E3');

title3 = {'Normal_Temperature_Bias/LSB'};
column = {'X','Y','Z'};
writecell(title3,Output_Excel_Filename,'Sheet','加表常温','Range','H1');
writecell(column,Output_Excel_Filename,'Sheet','加表常温','Range','H2');
writematrix(Acc_Bias,Output_Excel_Filename,'Sheet','加表常温','Range','H3');

title4 = {'Normal_Temperature_Scale_Factor/LSB'};
column = {'X','Y','Z'};
writecell(title4,Output_Excel_Filename,'Sheet','加表常温','Range','M1');
writecell(column,Output_Excel_Filename,'Sheet','加表常温','Range','M2');
writematrix([K_a(1,1),K_a(2,2),K_a(3,3)],Output_Excel_Filename,'Sheet','加表常温','Range','M3');

title5 = {'Misalignment_Compensation_Matrix'};
writecell(title5,Output_Excel_Filename,'Sheet','加表常温','Range','A5');
writematrix(K_a_std,Output_Excel_Filename,'Sheet','加表常温','Range','A6');

title6 = {'Bias_Correct_Val'};
column = {'X','Y','Z'};
writecell(title6,Output_Excel_Filename,'Sheet','加表常温','Range','F5');
writecell(column,Output_Excel_Filename,'Sheet','加表常温','Range','F6');
writematrix(Bias_Correct_Val,Output_Excel_Filename,'Sheet','加表常温','Range','F7');
%%陀螺全温
title1={'Temperature_Point/°C'};
column = {'X','Y','Z'};
writecell(title1,Output_Excel_Filename,'Sheet','陀螺全温','Range','A1');
writecell(column,Output_Excel_Filename,'Sheet','陀螺全温','Range','A2');
writematrix(Gyro_Temperature_All(1:end,1:3),Output_Excel_Filename,'Sheet','陀螺全温','Range','A3');

title2 = {'Board_Temperature/°C'};
column = {'Board'};
writecell(title2,Output_Excel_Filename,'Sheet','陀螺全温','Range','E1');
writecell(column,Output_Excel_Filename,'Sheet','陀螺全温','Range','E2');
writematrix(Gyro_Temperature_All(1:end,4),Output_Excel_Filename,'Sheet','陀螺全温','Range','E3');

title3={'Bias_Value/LSB'};
column = {'X','Y','Z'};
writecell(title3,Output_Excel_Filename,'Sheet','陀螺全温','Range','H1');
writecell(column,Output_Excel_Filename,'Sheet','陀螺全温','Range','H2');
writematrix(Gyro_Bias_All,Output_Excel_Filename,'Sheet','陀螺全温','Range','H3');

title4={'Scale_Factor/LSB'};
column = {'X','Y','Z'};
writecell(title4,Output_Excel_Filename,'Sheet','陀螺全温','Range','M1');
writecell(column,Output_Excel_Filename,'Sheet','陀螺全温','Range','M2');
writematrix(Gyro_Scale_Factor_all,Output_Excel_Filename,'Sheet','陀螺全温','Range','M3');

%%加速度计全温
title1={'Temperature_Point/°C'};
column = {'X','Y','Z'};
writecell(title1,Output_Excel_Filename,'Sheet','加表全温','Range','A1');
writecell(column,Output_Excel_Filename,'Sheet','加表全温','Range','A2');
writematrix(Acc_Temperature_All(1:end,1:3),Output_Excel_Filename,'Sheet','加表全温','Range','A3');

title2 = {'Board_Temperature/°C'};
column = {'Board'};
writecell(title2,Output_Excel_Filename,'Sheet','加表全温','Range','E1');
writecell(column,Output_Excel_Filename,'Sheet','加表全温','Range','E2');
writematrix(Acc_Temperature_All(1:end,4),Output_Excel_Filename,'Sheet','加表全温','Range','E3');

title3={'Bias_Value/LSB'};
column = {'X','Y','Z'};
writecell(title3,Output_Excel_Filename,'Sheet','加表全温','Range','H1');
writecell(column,Output_Excel_Filename,'Sheet','加表全温','Range','H2');
writematrix(Acc_Bias_All,Output_Excel_Filename,'Sheet','加表全温','Range','H3');

title4={'Scale_Factor/LSB'};
column = {'X','Y','Z'};
writecell(title4,Output_Excel_Filename,'Sheet','加表全温','Range','M1');
writecell(column,Output_Excel_Filename,'Sheet','加表全温','Range','M2');
writematrix(Acc_Scale_Factor_all,Output_Excel_Filename,'Sheet','加表全温','Range','M3');


