---
当前版本: 2024-12-04
---

# 概况

## 流程函数

- `CW70`
	- 用于计算常值零偏；
	- 更完整的测试流程需要在每个温度点都计算一次常值零偏，这意味着在每个温度点都要进行一次16位置+多速率旋转；
		- 这些数据用于计算温度比例因子、全温比例因子、非正交补偿矩阵等；
- `BD02_All_Temperature_Test_Data_Clean_for_70_12`
	- 全温测试数据清洗；
- `BD05_Acc_and_Gyro_Compensation_Verification_Two_Input_70_12`
	- 全温补偿验证；

## 库函数

- `BD_Artificial_Neural_Networks_Compen`
	- 和`Artificial_Neural_Networks_Compen`函数的内容完全一样；
- `shift_average_temp`
	- 计算移动窗口内的温度平均值，窗口大小通过输入参数控制；
