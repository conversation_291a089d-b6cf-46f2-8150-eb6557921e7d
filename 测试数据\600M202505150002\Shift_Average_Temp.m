function shift_average_temp = Shift_Average_Temp(Temp_Series,Num)
    shift_average_temp = zeros(length(Temp_Series),1);
    for i = 1:length(Temp_Series)
        if(i == 1)
           shift_average_temp(i,1) =  Temp_Series(i,1);
        elseif(i < Num)
           shift_average_temp(i,1) = mean(Temp_Series(1:i,1));
        else
            shift_average_temp(i,1) = mean(Temp_Series(i-Num + 1:i,1));
        end
        
    end
end