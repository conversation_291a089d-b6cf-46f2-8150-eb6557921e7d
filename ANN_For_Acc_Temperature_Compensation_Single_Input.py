# -*- coding: utf-8 -*-
#神经网络拟合惯导陀螺数据
import h5py
#import scipy.io as scio
from keras.models import Sequential
from keras.layers import Dense, Activation
from keras import optimizers
from keras.callbacks import EarlyStopping
import numpy as np
from keras.layers import Dropout,BatchNormalization,GaussianNoise

#from keras import regularizers

#def smooth(Series,num):
#    smooth_series = np.zeros(len(Series))
#    for i in range(len(Series)):
#        if i == 0:
#           smooth_series[i] = Series[i]
#        elif i < num:
#           smooth_series[i] = Series[:i].mean()
#        else:
#           smooth_series[i] = Series[i-num:i -1].mean()
#    return smooth_series
    

Acc_Path = ""   # All_Temperature_Acc.mat所在的路径
Output_Weight_and_Bias= ''

def load_mat_v73(file_path):
    with h5py.File(file_path, 'r') as f:
        # 提取所有变量到字典（可能需要调整键名）
        data = {key: np.array(f[key][()]) for key in f.keys()}
    return data

# 加载数据
Data = load_mat_v73(Acc_Path + 'All_Temperature_Acc.mat')
Train_Data = Data['SaveAccMat']  # 假设 'SaveAccMat' 是文件中的变量名

#Data = scio.loadmat(Acc_Path + 'All_Temperature_Acc.mat')
#Train_Data = Data['SaveAccMat']

#Data_2 = scio.loadmat(Acc_Path + 'All_Temperature_Acc_For_Valid.mat')
#Valid_Data = Data_2['SaveAccMat']
##
Train_Data_Diff_X = Train_Data[:,7]
Train_Data_Diff_Y = Train_Data[:,8]
Train_Data_Diff_Z = Train_Data[:,9]
#
#Valid_Data_Diff_X = Valid_Data[:,7]
#Valid_Data_Diff_Y = Valid_Data[:,8]
#Valid_Data_Diff_Z = Valid_Data[:,9]

length = len(Train_Data)
#length_Valid = len(Valid_Data)
####X陀螺数据归一化
Acc_X_Max = Train_Data[:,0].max()
Acc_X_Min = Train_Data[:,0].min()

Train_Data[:,0] = (Train_Data[:,0] - Acc_X_Min) / (Acc_X_Max - Acc_X_Min)
Acc_X_Mean = Train_Data[:,0].mean()
Acc_X_Output = Train_Data[:,0] - Acc_X_Mean


Acc_X_Output = Acc_X_Output.reshape((length,1))

Acc_X_Temp_Max = Train_Data[:,3].max()
Acc_X_Temp_Min = Train_Data[:,3].min()

Train_Data[:,3] = (Train_Data[:,3] - Acc_X_Temp_Min) / (Acc_X_Temp_Max - Acc_X_Temp_Min)
Acc_X_Temp_Mean = Train_Data[:,3].mean()
Acc_X_Input1 = Train_Data[:,3] - Acc_X_Temp_Mean
Acc_X_Input1 = Acc_X_Input1.reshape((length,1))

#Acc_X_Input = Acc_X_Input1
#Y陀螺数据归一化
Acc_Y_Max = Train_Data[:,1].max()
Acc_Y_Min = Train_Data[:,1].min()

Train_Data[:,1] = (Train_Data[:,1] - Acc_Y_Min) / (Acc_Y_Max - Acc_Y_Min)
Acc_Y_Mean =Train_Data[:,1].mean()
Acc_Y_Output = Train_Data[:,1] - Acc_Y_Mean

Acc_Y_Output = Acc_Y_Output.reshape((length,1))

Acc_Y_Temp_Max = Train_Data[:,4].max()
Acc_Y_Temp_Min = Train_Data[:,4].min()
Train_Data[:,4] = (Train_Data[:,4] - Acc_Y_Temp_Min) / (Acc_Y_Temp_Max - Acc_Y_Temp_Min)

Acc_Y_Temp_Mean = Train_Data[:,4].mean()
Acc_Y_Input1 = Train_Data[:,4] - Acc_Y_Temp_Mean

Acc_Y_Input1 = Acc_Y_Input1.reshape((length,1))

#Z陀螺数据归一化
Acc_Z_Max = Train_Data[:,2].max()
Acc_Z_Min = Train_Data[:,2].min()
Train_Data[:,2] = (Train_Data[:,2] - Acc_Z_Min) / (Acc_Z_Max - Acc_Z_Min)

Acc_Z_Mean = Train_Data[:,2].mean()
Acc_Z_Output = Train_Data[:,2] - Acc_Z_Mean


Acc_Z_Output = Acc_Z_Output.reshape((length,1))

Acc_Z_Temp_Max = Train_Data[:,5].max()
Acc_Z_Temp_Min = Train_Data[:,5].min()
Train_Data[:,5] = (Train_Data[:,5] - Acc_Z_Temp_Min) / (Acc_Z_Temp_Max - Acc_Z_Temp_Min)

Acc_Z_Temp_Mean = Train_Data[:,5].mean()
Acc_Z_Input1 = Train_Data[:,5] - Acc_Z_Temp_Mean

Acc_Z_Input1 = Acc_Z_Input1.reshape((length,1))

sample_weight = np.ones((Acc_Z_Input1.shape[0]))



#陀螺温度梯度的归一化
Acc_X_Temp_Diff_Max = Train_Data_Diff_X.max()
Acc_X_Temp_Diff_Min = Train_Data_Diff_X.min()

Train_Data_Diff_X = (Train_Data_Diff_X - Acc_X_Temp_Diff_Min) / (Acc_X_Temp_Diff_Max - Acc_X_Temp_Diff_Min)
Acc_X_Temp_Diff_Mean = Train_Data_Diff_X.mean()
Acc_X_Input2 = Train_Data_Diff_X - Acc_X_Temp_Diff_Mean
Acc_X_Input2 = Acc_X_Input2.reshape((length,1))


Acc_Y_Temp_Diff_Max = Train_Data_Diff_Y.max()
Acc_Y_Temp_Diff_Min = Train_Data_Diff_Y.min()

Train_Data_Diff_Y = (Train_Data_Diff_Y - Acc_Y_Temp_Diff_Min) / (Acc_Y_Temp_Diff_Max - Acc_Y_Temp_Diff_Min)
Acc_Y_Temp_Diff_Mean = Train_Data_Diff_Y.mean()
Acc_Y_Input2 = Train_Data_Diff_Y - Acc_Y_Temp_Diff_Mean
Acc_Y_Input2 = Acc_Y_Input2.reshape((length,1))


Acc_Z_Temp_Diff_Max = Train_Data_Diff_Z.max()
Acc_Z_Temp_Diff_Min = Train_Data_Diff_Z.min()

Train_Data_Diff_Z = (Train_Data_Diff_Z - Acc_Z_Temp_Diff_Min) / (Acc_Z_Temp_Diff_Max - Acc_Z_Temp_Diff_Min)
Acc_Z_Temp_Diff_Mean = Train_Data_Diff_Z.mean()
Acc_Z_Input2 = Train_Data_Diff_Z - Train_Data_Diff_Z.mean()
Acc_Z_Input2 = Acc_Z_Input2.reshape((length,1))

#X陀螺验证数据归一化
#Valid_Data[:,0] = (Valid_Data[:,0] - Acc_X_Min) / (Acc_X_Max - Acc_X_Min)
#Acc_X_Valid_Output = Valid_Data[:,0] - Acc_X_Mean
#Acc_X_Valid_Output = Acc_X_Valid_Output.reshape((length_Valid,1))

#Valid_Data[:,3] = (Valid_Data[:,3] - Acc_X_Temp_Min) / (Acc_X_Temp_Max - Acc_X_Temp_Min)
#Acc_X_Valid_Input1 = Valid_Data[:,3] - Acc_X_Temp_Mean
#Acc_X_Valid_Input1 = Acc_X_Valid_Input1.reshape((length_Valid,1))
#
#Valid_Data_Diff_X = (Valid_Data_Diff_X - Acc_X_Temp_Diff_Min) / (Acc_X_Temp_Diff_Max - Acc_X_Temp_Diff_Min)
#Acc_X_Valid_Input2 = Valid_Data_Diff_X - Acc_X_Temp_Diff_Mean
#Acc_X_Valid_Input2 = Acc_X_Valid_Input2.reshape((length_Valid,1))

#训练X陀螺
Acc_X_Input = np.concatenate((Acc_X_Input1,Acc_X_Input2),axis = 1)
#Acc_X_Valid_Input = np.concatenate((Acc_X_Valid_Input1,Acc_X_Valid_Input2),axis = 1)
model_X = Sequential()
model_X.add(Dense(units=8, input_dim=2))
model_X.add(Activation("relu"))
#model_X.add(Dropout(0.025))
model_X.add(Dense(units=4))
model_X.add(Activation("relu"))
#model_X.add(Dropout(0.05))
model_X.add(Dense(units=1))

sgd = optimizers.SGD(learning_rate=0.1, decay=1e-6, momentum=0.9, nesterov=True)
adam = optimizers.Adam(learning_rate=0.0015)
early_stop = EarlyStopping()
model_X.compile(loss='mse',optimizer=adam)

model_X.fit(Acc_X_Input, Acc_X_Output, batch_size=len(Acc_X_Input), epochs=6000, verbose=1,validation_split=0.001, shuffle=True)

np.savetxt(Output_Weight_and_Bias + 'Acc_X_dense_1_Mat.txt',model_X.layers[0].get_weights()[0])
np.savetxt(Output_Weight_and_Bias + 'Acc_X_dense_1_Bias.txt',model_X.layers[0].get_weights()[1])

np.savetxt(Output_Weight_and_Bias + 'Acc_X_dense_2_Mat.txt',model_X.layers[2].get_weights()[0])
np.savetxt(Output_Weight_and_Bias + 'Acc_X_dense_2_Bias.txt',model_X.layers[2].get_weights()[1])

np.savetxt(Output_Weight_and_Bias + 'Acc_X_dense_3_Mat.txt',model_X.layers[4].get_weights()[0])
np.savetxt(Output_Weight_and_Bias + 'Acc_X_dense_3_Bias.txt',model_X.layers[4].get_weights()[1])

with open(Output_Weight_and_Bias + "XAcc_Para.txt", "w") as f:
    np.savetxt(f, model_X.layers[0].get_weights()[0],comments='',delimiter = ',',header = "DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {",footer = "};",newline = ",\n")
    np.savetxt(f, model_X.layers[0].get_weights()[1],comments='',delimiter = ',',header = "DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {",footer = "};",newline = ",\n")
    
    np.savetxt(f, model_X.layers[2].get_weights()[0],comments='',delimiter = ',',header = "DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {",footer = "};",newline = ",\n")
    np.savetxt(f, model_X.layers[2].get_weights()[1],comments='',delimiter = ',',header = "DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {",footer = "};",newline = ",\n")
    
    np.savetxt(f, model_X.layers[4].get_weights()[0],comments='',delimiter = ',',header = "DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { ",footer = "};",newline = ",\n")
    np.savetxt(f, model_X.layers[4].get_weights()[1],comments='',delimiter = ',',header = "DPARA Dense3_Bias = ",footer = ";\n")
    
    f.write("Normalized_Temp_Max =" + str(Acc_X_Temp_Max) + ";\n")
    f.write("Normalized_Temp_Min =" + str(Acc_X_Temp_Min) + ";\n")
    f.write("Normalized_Temp_Mean =" + str(Acc_X_Temp_Mean) + ";\n")
    
    
    f.write("Normalized_Temp_Diff_Max =" + str(Acc_X_Temp_Diff_Max) + ";\n")
    f.write("Normalized_Temp_Diff_Min =" + str(Acc_X_Temp_Diff_Min) + ";\n")
    f.write("Normalized_Temp_Diff_Mean =" + str(Acc_X_Temp_Diff_Mean) + ";\n")
    
    
    f.write("Normalized_Output_Max =" + str(Acc_X_Max) + ";\n")
    f.write("Normalized_Output_Min =" + str(Acc_X_Min) + ";\n")
    f.write("Normalized_Output_Mean =" + str(Acc_X_Mean) + ";\n")

##训练Y陀螺
Acc_Y_Input = np.concatenate((Acc_Y_Input1,Acc_Y_Input2),axis = 1)
model_Y = Sequential()
model_Y.add(Dense(units=8, input_dim=2))
model_Y.add(Activation("relu"))
#model_Y.add(Dropout(0.05))
model_Y.add(Dense(units=4))
model_Y.add(Activation("relu"))
#model_Y.add(Dropout(0.05))
model_Y.add(Dense(units=1))

sgd = optimizers.SGD(learning_rate=0.1, decay=1e-6, momentum=0.9, nesterov=True)
adam = optimizers.Adam(learning_rate=0.0015)
early_stop = EarlyStopping()
model_Y.compile(loss='mse',optimizer=adam)

model_Y.fit(Acc_Y_Input, Acc_Y_Output, batch_size=len(Acc_X_Input1), epochs=6000, verbose=1,validation_split=0.001, shuffle=True)

np.savetxt(Output_Weight_and_Bias + 'Acc_Y_dense_1_Mat.txt',model_Y.layers[0].get_weights()[0])
np.savetxt(Output_Weight_and_Bias + 'Acc_Y_dense_1_Bias.txt',model_Y.layers[0].get_weights()[1])

np.savetxt(Output_Weight_and_Bias + 'Acc_Y_dense_2_Mat.txt',model_Y.layers[2].get_weights()[0])
np.savetxt(Output_Weight_and_Bias + 'Acc_Y_dense_2_Bias.txt',model_Y.layers[2].get_weights()[1])

np.savetxt(Output_Weight_and_Bias + 'Acc_Y_dense_3_Mat.txt',model_Y.layers[4].get_weights()[0])
np.savetxt(Output_Weight_and_Bias + 'Acc_Y_dense_3_Bias.txt',model_Y.layers[4].get_weights()[1])

with open(Output_Weight_and_Bias + "YAcc_Para.txt", "w") as f:
    np.savetxt(f, model_Y.layers[0].get_weights()[0],comments='',delimiter = ',',header = "DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {",footer = "};",newline = ",\n")
    np.savetxt(f, model_Y.layers[0].get_weights()[1],comments='',delimiter = ',',header = "DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {",footer = "};",newline = ",\n")
    
    np.savetxt(f, model_Y.layers[2].get_weights()[0],comments='',delimiter = ',',header = "DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {",footer = "};",newline = ",\n")
    np.savetxt(f, model_Y.layers[2].get_weights()[1],comments='',delimiter = ',',header = "DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {",footer = "};",newline = ",\n")
    
    np.savetxt(f, model_Y.layers[4].get_weights()[0],comments='',delimiter = ',',header = "DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { ",footer = "};",newline = ",\n")
    np.savetxt(f, model_Y.layers[4].get_weights()[1],comments='',delimiter = ',',header = "DPARA Dense3_Bias = ",footer = ";\n")
    
    f.write("Normalized_Temp_Max =" + str(Acc_Y_Temp_Max) + ";\n")
    f.write("Normalized_Temp_Min =" + str(Acc_Y_Temp_Min) + ";\n")
    f.write("Normalized_Temp_Mean =" + str(Acc_Y_Temp_Mean) + ";\n")
    
    
    f.write("Normalized_Temp_Diff_Max =" + str(Acc_Y_Temp_Diff_Max) + ";\n")
    f.write("Normalized_Temp_Diff_Min =" + str(Acc_Y_Temp_Diff_Min) + ";\n")
    f.write("Normalized_Temp_Diff_Mean =" + str(Acc_Y_Temp_Diff_Mean) + ";\n")
    
    
    f.write("Normalized_Output_Max =" + str(Acc_Y_Max) + ";\n")
    f.write("Normalized_Output_Min =" + str(Acc_Y_Min) + ";\n")
    f.write("Normalized_Output_Mean =" + str(Acc_Y_Mean) + ";\n")
#
#训练Z陀螺
Acc_Z_Input = np.concatenate((Acc_Z_Input1,Acc_Z_Input2),axis = 1)
model_Z = Sequential()
model_Z.add(Dense(units=8, input_dim=2))
model_Z.add(Activation("relu"))
#model_Z.add(Dropout(0.1))
model_Z.add(Dense(units=4))
model_Z.add(Activation("relu"))
#model_Z.add(Dropout(0.05))
model_Z.add(Dense(units=1))

sgd = optimizers.SGD(learning_rate=0.1, decay=1e-6, momentum=0.9, nesterov=True)
adam = optimizers.Adam(learning_rate=0.0015)
early_stop = EarlyStopping()
model_Z.compile(loss='mse',optimizer=adam)

model_Z.fit(Acc_Z_Input, Acc_Z_Output, batch_size=len(Acc_X_Input1), epochs=6000, verbose=1,validation_split=0.001,shuffle=True)

np.savetxt(Output_Weight_and_Bias + 'Acc_Z_dense_1_Mat.txt',model_Z.layers[0].get_weights()[0])
np.savetxt(Output_Weight_and_Bias + 'Acc_Z_dense_1_Bias.txt',model_Z.layers[0].get_weights()[1])

np.savetxt(Output_Weight_and_Bias + 'Acc_Z_dense_2_Mat.txt',model_Z.layers[2].get_weights()[0])
np.savetxt(Output_Weight_and_Bias + 'Acc_Z_dense_2_Bias.txt',model_Z.layers[2].get_weights()[1])

np.savetxt(Output_Weight_and_Bias + 'Acc_Z_dense_3_Mat.txt',model_Z.layers[4].get_weights()[0])
np.savetxt(Output_Weight_and_Bias + 'Acc_Z_dense_3_Bias.txt',model_Z.layers[4].get_weights()[1])
###
with open(Output_Weight_and_Bias + "ZAcc_Para.txt", "w") as f:
    np.savetxt(f, model_Z.layers[0].get_weights()[0],comments='',delimiter = ',',header = "DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {",footer = "};",newline = ",\n")
    np.savetxt(f, model_Z.layers[0].get_weights()[1],comments='',delimiter = ',',header = "DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {",footer = "};",newline = ",\n")
    
    np.savetxt(f, model_Z.layers[2].get_weights()[0],comments='',delimiter = ',',header = "DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {",footer = "};",newline = ",\n")
    np.savetxt(f, model_Z.layers[2].get_weights()[1],comments='',delimiter = ',',header = "DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {",footer = "};",newline = ",\n")
    
    np.savetxt(f, model_Z.layers[4].get_weights()[0],comments='',delimiter = ',',header = "DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { ",footer = "};",newline = ",\n")
    np.savetxt(f, model_Z.layers[4].get_weights()[1],comments='',delimiter = ',',header = "DPARA Dense3_Bias = ",footer = ";\n")
    
    f.write("Normalized_Temp_Max =" + str(Acc_Z_Temp_Max) + ";\n")
    f.write("Normalized_Temp_Min =" + str(Acc_Z_Temp_Min) + ";\n")
    f.write("Normalized_Temp_Mean =" + str(Acc_Z_Temp_Mean) + ";\n")
    
    
    f.write("Normalized_Temp_Diff_Max =" + str(Acc_Z_Temp_Diff_Max) + ";\n")
    f.write("Normalized_Temp_Diff_Min =" + str(Acc_Z_Temp_Diff_Min) + ";\n")
    f.write("Normalized_Temp_Diff_Mean =" + str(Acc_Z_Temp_Diff_Mean) + ";\n")
    
    
    f.write("Normalized_Output_Max =" + str(Acc_Z_Max) + ";\n")
    f.write("Normalized_Output_Min =" + str(Acc_Z_Min) + ";\n")
    f.write("Normalized_Output_Mean =" + str(Acc_Z_Mean) + ";\n")

